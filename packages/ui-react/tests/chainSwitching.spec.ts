import { testWithSynpress } from "@synthetixio/synpress"
import { MetaMask, metaMaskFixtures } from "@synthetixio/synpress/playwright"
import {
  closeAllDialogs,
  extractBalance,
  verifyCanPlayAgain,
  waitForBettingStates,
} from "../test/helpers/testHelpers"
import basicSetup from "../test/wallet-setup/basic.setup"

const test = testWithSynpress(metaMaskFixtures(basicSetup))
const { expect } = test

test.describe("Chain Switching Tests", () => {
  test("should switch from Base to Polygon and play coin toss", async ({
    context,
    page,
    metamaskPage,
    extensionId,
  }) => {
    const metamask = new MetaMask(context, metamaskPage, basicSetup.walletPassword, extensionId)

    // Navigate to coin toss game
    await page.goto("/coinToss.html")
    await page.waitForLoadState("networkidle")

    // Connect wallet (starts on Base network)
    console.log("\n=== CONNECTING WALLET ON BASE ===")
    const connectButton = page.getByTestId("ockConnectButton")
    await expect(connectButton).toBeVisible()
    await connectButton.click()

    const onchainkitModal = page.locator('[data-testid="ockModalOverlay"]')
    await expect(onchainkitModal).toBeVisible()
    const metamaskBtn = onchainkitModal.getByRole("button").filter({ hasText: /metamask/i })
    await metamaskBtn.click()

    await metamask.connectToDapp()
    const address = await metamask.getAccountAddress()
    console.log("Connected wallet address:", address)

    // Wait for wallet connection to complete
    const walletConnectedBtn = page.locator('[data-testid="ockConnectWallet_Connected"]')
    await expect(walletConnectedBtn).toBeVisible({ timeout: 10000 })

    // Verify we're on Base initially
    console.log("\n=== VERIFYING BASE NETWORK ===")
    // The balance should show ETH (Base native token)
    const balanceElement = page.locator("text=/Balance:/").first()
    await expect(balanceElement).toBeVisible({ timeout: 20000 })

    // Switch to Polygon network (already added during setup)
    console.log("\n=== SWITCHING TO POLYGON ===")
    try {
      await metamask.switchNetwork("Polygon")
      console.log("Switched to Polygon network in MetaMask")

      // Handle the "Got it" popup that appears after switching
      await metamaskPage.waitForTimeout(1000)
      const gotItButton = metamaskPage.locator('button:has-text("Got it")')
      const hasGotItButton = await gotItButton.isVisible({ timeout: 5000 }).catch(() => false)

      if (hasGotItButton) {
        console.log("Found 'Got it' button, clicking it")
        await gotItButton.click()
        await metamaskPage.waitForTimeout(500)
      }
    } catch (error) {
      console.log("Error switching to Polygon:", error)
      throw error
    }

    // Look for chain switch button in the app UI
    console.log("\n=== CHECKING FOR CHAIN SWITCH UI ===")

    // The app should detect we're on wrong chain and show switch button
    const switchChainBtn = page.locator('button:has-text("Switch"), button:has-text("chain")')
    const hasSwitchButton = await switchChainBtn.isVisible({ timeout: 10000 }).catch(() => false)

    if (hasSwitchButton) {
      console.log("Found chain switch button, clicking it")
      await switchChainBtn.click()

      // Wait for MetaMask to handle the switch
      await page.waitForTimeout(2000)
    } else {
      console.log("No chain switch button found, app might have auto-switched")
    }

    // Wait for the UI to update and show Polygon
    await page.waitForTimeout(3000)

    // Refresh the page to ensure balance is updated
    await page.reload()
    await page.waitForLoadState("networkidle")

    // Check balance on Polygon (should show MATIC)
    console.log("\n=== CHECKING POLYGON BALANCE ===")
    const balanceElementAfterSwitch = page.locator("text=/Balance:/").first()
    await expect(balanceElementAfterSwitch).toBeVisible({ timeout: 20000 })

    const polygonBalanceText = await balanceElementAfterSwitch.locator("..").first().textContent()
    console.log("Polygon balance text:", polygonBalanceText)

    const polygonBalance = extractBalance(polygonBalanceText)
    console.log("Polygon balance amount:", polygonBalance)

    // Check if wallet needs MATIC tokens
    if (polygonBalance === 0) {
      console.log("\n⚠️  WALLET NEEDS MATIC TOKENS ON POLYGON")
      console.log(`Please send at least 0.1 MATIC to ${address} on Polygon chain`)
      console.log("You can get MATIC from:")
      console.log("- Polygon faucet: https://faucet.polygon.technology/")
      console.log("- Bridge from another chain")
      console.log("- Buy on an exchange and withdraw to Polygon")
      await page.screenshot({ path: "polygon-needs-funding.png", fullPage: true })

      // Don't throw error, just log the requirement
      console.log("⚠️  Skipping game play due to insufficient MATIC balance")
      return
    }

    // Play coin toss on Polygon
    console.log("\n=== PLAYING COIN TOSS ON POLYGON ===")

    // Enter bet amount (smaller amount for MATIC)
    const betAmountInput = page.locator("#betAmount")
    await expect(betAmountInput).toBeVisible()
    await betAmountInput.clear()
    await betAmountInput.fill("0.01") // 0.01 MATIC bet
    console.log("Bet amount: 0.01 MATIC")

    // Select heads
    const coinButton = page.locator('button[aria-label*="Select"][aria-label*="side"]')
    await expect(coinButton).toBeVisible({ timeout: 5000 })

    const ariaLabel = await coinButton.getAttribute("aria-label")
    console.log("Current coin button aria-label:", ariaLabel)

    if (ariaLabel?.includes("Select Heads")) {
      await coinButton.click()
      console.log("Selected Heads")
    } else {
      console.log("Heads already selected")
    }

    // Click play button
    const playButton = page.locator('button:has-text("Place Bet"), button:has-text("Try again")')
    await expect(playButton).toBeVisible({ timeout: 10000 })
    await expect(playButton).toBeEnabled()

    await playButton.click()
    console.log("Clicked Place Bet button")

    // Confirm transaction in MetaMask
    await metamask.confirmTransaction()
    console.log("Transaction confirmed in MetaMask")

    // Wait for bet to be processed
    await waitForBettingStates(page)

    // Check for result
    console.log("Checking for game result...")

    const resultModal = page.locator('[role="dialog"]').filter({ hasText: /You (won|lost)/i })
    const hasResultModal = await resultModal.isVisible({ timeout: 10000 }).catch(() => false)

    let isWin = false
    if (hasResultModal) {
      const resultText = await resultModal.textContent()
      isWin = resultText?.toLowerCase().includes("won") || false
      console.log(`\n🎰 POLYGON RESULT: ${isWin ? "WON! 🎉" : "Lost 😢"}`)

      // Close result modal
      const resultCloseButton = resultModal.locator('button[aria-label="Close"]')
      if (await resultCloseButton.isVisible()) {
        await resultCloseButton.click()
      }
    } else {
      // Determine result from balance change
      console.log("No result modal found, determining result from balance...")

      const currentBalanceText = await balanceElementAfterSwitch.locator("..").first().textContent()
      const currentBalance = extractBalance(currentBalanceText)

      if (currentBalance > polygonBalance - 0.01) {
        isWin = true
        console.log(`\n🎰 POLYGON RESULT: WON! 🎉 (${polygonBalance} → ${currentBalance} MATIC)`)
      } else {
        isWin = false
        console.log(`\n🎰 POLYGON RESULT: Lost 😢 (${polygonBalance} → ${currentBalance} MATIC)`)
      }
    }

    // Close any open dialogs
    await closeAllDialogs(page)

    // Verify final state
    console.log("\n=== VERIFYING FINAL STATE ===")
    const finalBalanceText = await balanceElementAfterSwitch.locator("..").first().textContent()
    const finalBalance = extractBalance(finalBalanceText)
    console.log("Final Polygon balance:", finalBalance, "MATIC")

    // Verify balance changed
    const balanceChanged = Math.abs(finalBalance - polygonBalance) > 0.001
    expect(balanceChanged).toBe(true)

    if (isWin) {
      expect(finalBalance).toBeGreaterThan(polygonBalance - 0.01)
    } else {
      expect(finalBalance).toBeLessThan(polygonBalance)
    }

    // Verify we can play again
    const canPlayAgain = await verifyCanPlayAgain(page, "polygon-cannot-play-again.png")
    expect(canPlayAgain).toBe(true)

    console.log("\n✅ Chain switching test completed successfully!")
    console.log("Switched from Base to Polygon and played coin toss")
    console.log(`Balance change: ${polygonBalance} MATIC → ${finalBalance} MATIC`)
  })
})
